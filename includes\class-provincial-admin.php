<?php
/**
 * Provincial Administration Admin Class
 * 
 * Handles admin interface for the Provincial Administration Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Admin {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('wp_ajax_esp_slideshow_action', array($this, 'handle_slideshow_ajax'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Check if user has any provincial admin access
        $has_admin_access = current_user_can('view_provincial_admin') ||
                           current_user_can('view_district_admin') ||
                           current_user_can('manage_options');

        if (!$has_admin_access) {
            return;
        }

        // Determine the appropriate capability for main menu access
        // Administrators should always have access, then check for specific capabilities
        $main_menu_capability = current_user_can('manage_options') ? 'manage_options' : 'view_provincial_admin';

        // Main menu
        add_menu_page(
            __('Provincial Administration', 'esp-admin-manager'),
            __('Provincial Administration', 'esp-admin-manager'),
            $main_menu_capability,
            'provincial-admin-dashboard',
            array($this, 'dashboard_page'),
            'dashicons-building',
            30
        );

        // Dashboard submenu
        add_submenu_page(
            'provincial-admin-dashboard',
            __('Dashboard', 'esp-admin-manager'),
            __('Dashboard', 'esp-admin-manager'),
            $main_menu_capability,
            'provincial-admin-dashboard',
            array($this, 'dashboard_page')
        );

        // Governor submenu (Provincial users and administrators only)
        if (current_user_can('manage_governor_info') || current_user_can('manage_options')) {
            $governor_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_governor_info';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Governor Profile', 'esp-admin-manager'),
                __('Governor Profile', 'esp-admin-manager'),
                $governor_capability,
                'provincial-admin-governor',
                array($this, 'governor_page')
            );
        }

        // MPs submenu
        if (current_user_can('manage_district_mps') || current_user_can('manage_provincial_mps') || current_user_can('manage_options')) {
            $mps_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_mps';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Members of Parliament', 'esp-admin-manager'),
                __('MPs', 'esp-admin-manager'),
                $mps_capability,
                'provincial-admin-mps',
                array($this, 'mps_page')
            );
        }

        // Districts submenu
        if (current_user_can('manage_district_info') || current_user_can('manage_options')) {
            $districts_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_info';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Districts', 'esp-admin-manager'),
                __('Districts', 'esp-admin-manager'),
                $districts_capability,
                'provincial-admin-districts',
                array($this, 'districts_page')
            );
        }

        // Statistics submenu
        if (current_user_can('manage_provincial_statistics') || current_user_can('manage_district_statistics') || current_user_can('manage_options')) {
            $statistics_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_statistics';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Provincial Statistics', 'esp-admin-manager'),
                __('Statistics', 'esp-admin-manager'),
                $statistics_capability,
                'provincial-admin-statistics',
                array($this, 'statistics_page')
            );
        }

        // Administrative Structure submenu (Provincial users and administrators only)
        if (current_user_can('manage_provincial_info') || current_user_can('manage_options')) {
            $structure_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_provincial_info';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Administrative Structure', 'esp-admin-manager'),
                __('Admin Structure', 'esp-admin-manager'),
                $structure_capability,
                'provincial-admin-structure',
                array($this, 'structure_page')
            );
        }

        // Events submenu
        if (current_user_can('manage_provincial_events') || current_user_can('manage_district_events') || current_user_can('manage_options')) {
            $events_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_events';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Events', 'esp-admin-manager'),
                __('Events', 'esp-admin-manager'),
                $events_capability,
                'provincial-admin-events',
                array($this, 'events_page')
            );
        }

        // News submenu
        if (current_user_can('manage_provincial_news') || current_user_can('manage_district_news') || current_user_can('manage_options')) {
            $news_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_news';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('News', 'esp-admin-manager'),
                __('News', 'esp-admin-manager'),
                $news_capability,
                'provincial-admin-news',
                array($this, 'news_page')
            );
        }

        // Contact submenu
        if (current_user_can('manage_provincial_contacts') || current_user_can('manage_district_contacts') || current_user_can('manage_options')) {
            $contact_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_contacts';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Contact Information', 'esp-admin-manager'),
                __('Contact Info', 'esp-admin-manager'),
                $contact_capability,
                'provincial-admin-contact',
                array($this, 'contact_page')
            );
        }

        // Slideshows submenu - administrators always have access
        if (current_user_can('manage_options') || current_user_can('manage_provincial_slideshows')) {
            $slideshow_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_provincial_slideshows';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Slideshows', 'esp-admin-manager'),
                __('Slideshows', 'esp-admin-manager'),
                $slideshow_capability,
                'provincial-admin-slideshows',
                array($this, 'slideshows_page')
            );

            add_submenu_page(
                'provincial-admin-dashboard',
                __('Add New Slideshow', 'esp-admin-manager'),
                __('Add Slideshow', 'esp-admin-manager'),
                $slideshow_capability,
                'provincial-admin-slideshows-add',
                array($this, 'add_slideshow_page')
            );
        }

        // Maps submenu
        if (current_user_can('edit_posts') || current_user_can('manage_options')) {
            $maps_capability = current_user_can('manage_options') ? 'manage_options' : 'edit_posts';
            add_submenu_page(
                'provincial-admin-dashboard',
                __('Maps', 'esp-admin-manager'),
                __('Maps', 'esp-admin-manager'),
                $maps_capability,
                'provincial-admin-maps',
                array($this, 'maps_page')
            );
        }

        // User Management submenu (Administrators only)
        if (current_user_can('manage_options')) {
            add_submenu_page(
                'provincial-admin-dashboard',
                __('User Management', 'esp-admin-manager'),
                __('User Management', 'esp-admin-manager'),
                'manage_options',
                'provincial-admin-users',
                array($this, 'user_management_page')
            );
        }
    }
    
    /**
     * Initialize admin settings
     */
    public function admin_init() {
        // Register settings
        register_setting('provincial_statistics_group', 'esp_provincial_statistics');
        register_setting('provincial_contact_group', 'esp_contact_information');
        register_setting('provincial_structure_group', 'esp_administrative_structure');

        // Handle form submissions early to avoid header issues
        $this->handle_early_form_submissions();
    }
    
    /**
     * Dashboard page
     */
    public function dashboard_page() {
        // Show debug helper if requested
        if (isset($_GET['debug']) && $_GET['debug'] == '1' && current_user_can('manage_options')) {
            include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/esp-debug-helper.php';
            return;
        }

        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/dashboard.php';
    }
    
    /**
     * Governor page
     */
    public function governor_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['provincial_nonce'], 'provincial_governor_nonce')) {
            $this->save_governor_data();
        }
        
        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/governor.php';
    }
    
    /**
     * MPs page
     */
    public function mps_page() {
        // Enqueue custom MPs styles and scripts
        wp_enqueue_style('esp-mps-custom', PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'admin/css/mps-custom.css', array(), PROVINCIAL_ADMIN_MANAGER_VERSION);
        wp_enqueue_script('esp-mps-custom', PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'admin/js/mps-custom.js', array('jquery', 'media-upload', 'media-views'), PROVINCIAL_ADMIN_MANAGER_VERSION, true);

        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/mps-custom.php';
    }
    
    /**
     * Handle form submissions early to avoid header issues
     */
    private function handle_early_form_submissions() {
        // Only handle on the districts admin page
        if (!isset($_GET['page']) || $_GET['page'] !== 'provincial-admin-districts') {
            return;
        }

        // Handle district form submissions (POST requests)
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['create_district', 'update_district'])) {
            $this->handle_district_actions();
        }

        // Handle district deletion (GET requests)
        if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['district_id'])) {
            $this->handle_district_delete();
        }
    }

    /**
     * Districts page
     */
    public function districts_page() {
        // Determine which view to show based on action
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';

        // Enqueue custom districts styles for all views
        wp_enqueue_style('esp-districts-custom', PROVINCIAL_ADMIN_MANAGER_PLUGIN_URL . 'admin/css/districts-custom.css', array(), PROVINCIAL_ADMIN_MANAGER_VERSION);

        switch ($action) {
            case 'create':
                if (!current_user_can('manage_options')) {
                    wp_die(__('You do not have sufficient permissions to access this page.', 'esp-admin-manager'));
                }
                // Enqueue media scripts for photo upload
                wp_enqueue_media();
                include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/district-create.php';
                break;

            case 'edit':
                // Enqueue media scripts for photo upload
                wp_enqueue_media();
                include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/district-edit.php';
                break;

            case 'delete':
                // Deletion is handled in handle_early_form_submissions
                // If we reach here, redirect to list
                wp_redirect(admin_url('admin.php?page=provincial-admin-districts'));
                exit;

            default:
                include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/districts-custom.php';
                break;
        }
    }

    /**
     * Handle district actions (create, update, delete)
     */
    private function handle_district_actions() {
        $districts_controller = Provincial_Districts_Controller::get_instance();

        if (isset($_POST['action'])) {
            $action = sanitize_text_field($_POST['action']);

            switch ($action) {
                case 'create_district':
                    $result = $districts_controller->create_district($_POST);

                    if (is_wp_error($result)) {
                        add_settings_error('esp_messages', 'esp_message', $result->get_error_message(), 'error');
                    } else {
                        // Handle photo upload
                        if (!empty($_POST['district_photo_id'])) {
                            set_post_thumbnail($result, intval($_POST['district_photo_id']));
                        }

                        add_settings_error('esp_messages', 'esp_message', __('District created successfully.', 'esp-admin-manager'), 'updated');
                        // Redirect to districts list
                        wp_redirect(admin_url('admin.php?page=provincial-admin-districts'));
                        exit;
                    }
                    break;

                case 'update_district':
                    $district_id = intval($_POST['district_id']);
                    $result = $districts_controller->update_district($district_id, $_POST);

                    if (is_wp_error($result)) {
                        add_settings_error('esp_messages', 'esp_message', $result->get_error_message(), 'error');
                    } else {
                        // Handle photo upload
                        if (!empty($_POST['district_photo_id'])) {
                            set_post_thumbnail($district_id, intval($_POST['district_photo_id']));
                        } elseif (isset($_POST['remove_photo'])) {
                            delete_post_thumbnail($district_id);
                        }

                        add_settings_error('esp_messages', 'esp_message', __('District updated successfully.', 'esp-admin-manager'), 'updated');
                        // Redirect to districts list
                        wp_redirect(admin_url('admin.php?page=provincial-admin-districts'));
                        exit;
                    }
                    break;
            }
        }
    }

    /**
     * Handle district deletion
     */
    private function handle_district_delete() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to perform this action.'));
        }

        $district_id = isset($_GET['district_id']) ? intval($_GET['district_id']) : 0;
        $nonce = isset($_GET['nonce']) ? sanitize_text_field($_GET['nonce']) : '';

        if (!$district_id || !wp_verify_nonce($nonce, 'esp_district_delete_' . $district_id)) {
            wp_die(__('Security check failed.'));
        }

        $districts_controller = Provincial_Districts_Controller::get_instance();
        $result = $districts_controller->delete_district($district_id);

        if (is_wp_error($result)) {
            add_settings_error('esp_messages', 'esp_message', $result->get_error_message(), 'error');
        } else {
            add_settings_error('esp_messages', 'esp_message', __('District moved to trash successfully.', 'esp-admin-manager'), 'updated');
        }

        // Redirect to districts list
        wp_redirect(admin_url('admin.php?page=provincial-admin-districts'));
        exit;
    }

    /**
     * Statistics page
     */
    public function statistics_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['provincial_nonce'], 'provincial_statistics_nonce')) {
            $this->save_statistics_data();
        }
        
        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/statistics.php';
    }
    
    /**
     * Administrative structure page
     */
    public function structure_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['provincial_nonce'], 'provincial_structure_nonce')) {
            $this->save_structure_data();
        }
        
        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/admin-structure.php';
    }
    
    /**
     * Events page
     */
    public function events_page() {
        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/events-custom.php';
    }
    
    /**
     * News page
     */
    public function news_page() {
        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/news.php';
    }

    /**
     * Contact page
     */
    public function contact_page() {
        // Handle debug actions
        if (isset($_POST['flush_rewrite_rules']) && current_user_can('manage_options')) {
            flush_rewrite_rules();
            add_settings_error('provincial_messages', 'provincial_message', __('Rewrite rules flushed successfully.', 'esp-admin-manager'), 'updated');
        }

        if (isset($_POST['create_test_contact']) && current_user_can('manage_options')) {
            $this->create_test_contact();
        }

        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/contact.php';
    }

    /**
     * Create a test contact for debugging
     */
    private function create_test_contact() {
        $contact_id = wp_insert_post(array(
            'post_title' => 'Test Emergency Services',
            'post_content' => 'This is a test contact created for debugging purposes.',
            'post_type' => 'esp_contact',
            'post_status' => 'publish'
        ));

        if ($contact_id && !is_wp_error($contact_id)) {
            // Add some test meta data
            update_post_meta($contact_id, '_esp_contact_address', 'Test Address, Test City, PNG');
            update_post_meta($contact_id, '_esp_contact_phone', '(+*************');
            update_post_meta($contact_id, '_esp_contact_email', '<EMAIL>');
            update_post_meta($contact_id, '_esp_contact_office_hours', 'Monday - Friday, 8:00 AM - 5:00 PM');

            add_settings_error('provincial_messages', 'provincial_message',
                sprintf(__('Test contact created successfully! ID: %d. Shortcode: [dakoii_contact id="%d"]', 'esp-admin-manager'),
                $contact_id, $contact_id), 'updated');
        } else {
            add_settings_error('provincial_messages', 'provincial_error',
                __('Failed to create test contact. Check if esp_contact post type is registered.', 'esp-admin-manager'), 'error');
        }
    }

    /**
     * User management page
     */
    public function user_management_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        $user_management = Provincial_User_Management::get_instance();
        $user_management->user_management_page();
    }
    
    /**
     * Save governor data
     */
    private function save_governor_data() {
        // Get the governor post or create one
        $governors = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1
        ));

        if (!empty($governors)) {
            $governor_id = $governors[0]->ID;
        } else {
            // Create governor post if it doesn't exist
            $governor_id = wp_insert_post(array(
                'post_title' => sanitize_text_field($_POST['governor_name']),
                'post_content' => wp_kses_post($_POST['governor_message']),
                'post_status' => 'publish',
                'post_type' => 'esp_governor'
            ));
        }

        if ($governor_id) {
            // Update post
            wp_update_post(array(
                'ID' => $governor_id,
                'post_title' => sanitize_text_field($_POST['governor_name']),
                'post_content' => wp_kses_post($_POST['governor_message'])
            ));

            // Update meta fields
            update_post_meta($governor_id, '_esp_governor_title', sanitize_text_field($_POST['governor_title']));
            update_post_meta($governor_id, '_esp_governor_party', sanitize_text_field($_POST['governor_party']));
            update_post_meta($governor_id, '_esp_governor_email', sanitize_email($_POST['governor_email']));
            update_post_meta($governor_id, '_esp_governor_phone', sanitize_text_field($_POST['governor_phone']));

            // Update photo if provided
            if (!empty($_POST['governor_photo']) && is_numeric($_POST['governor_photo'])) {
                set_post_thumbnail($governor_id, intval($_POST['governor_photo']));
            }

            add_settings_error('provincial_messages', 'provincial_message', __('Governor profile updated successfully.', 'esp-admin-manager'), 'updated');
        }
    }
    
    /**
     * Save statistics data
     */
    private function save_statistics_data() {
        $statistics = array(
            'population' => sanitize_text_field($_POST['population']),
            'area' => sanitize_text_field($_POST['area']),
            'districts' => sanitize_text_field($_POST['districts']),
            'llgs' => sanitize_text_field($_POST['llgs']),
            'wards' => sanitize_text_field($_POST['wards']),
            'urban_llgs' => sanitize_text_field($_POST['urban_llgs'])
        );
        
        update_option('esp_provincial_statistics', $statistics);
        add_settings_error('provincial_messages', 'provincial_message', __('Provincial statistics updated successfully.', 'esp-admin-manager'), 'updated');
    }
    
    /**
     * Save structure data
     */
    private function save_structure_data() {
        $structure = array(
            'government_sectors' => wp_kses_post($_POST['government_sectors']),
            'administrative_divisions' => wp_kses_post($_POST['administrative_divisions'])
        );

        update_option('esp_administrative_structure', $structure);
        add_settings_error('provincial_messages', 'provincial_message', __('Administrative structure updated successfully.', 'esp-admin-manager'), 'updated');
    }

    /**
     * Slideshows page
     */
    public function slideshows_page() {
        // Check permissions - administrators should always have access
        if (!current_user_can('manage_options') && !current_user_can('manage_provincial_slideshows')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'esp-admin-manager'));
        }

        // Check table status
        $table_status = Provincial_Slideshow::tables_exist();
        if (!$table_status['both']) {
            add_settings_error('slideshow_messages', 'slideshow_warning',
                __('Slideshow database tables are missing. Attempting to create them...', 'esp-admin-manager'), 'notice-warning');

            // Try to create tables
            Provincial_Slideshow::create_tables();
        }

        // Handle success messages from redirects
        if (isset($_GET['message'])) {
            switch ($_GET['message']) {
                case 'group_created':
                    add_settings_error('slideshow_messages', 'slideshow_message',
                        __('Slideshow group created successfully.', 'esp-admin-manager'), 'updated');
                    break;
            }
        }

        $groups = Provincial_Slideshow::get_groups();

        // Handle form submissions
        if (isset($_POST['action']) && wp_verify_nonce($_POST['_wpnonce'], 'esp_slideshow_action')) {
            switch ($_POST['action']) {
                case 'delete_group':
                    if (isset($_POST['group_id'])) {
                        Provincial_Slideshow::delete_group(intval($_POST['group_id']));
                        add_settings_error('slideshow_messages', 'slideshow_message',
                            __('Slideshow group deleted successfully.', 'esp-admin-manager'), 'updated');
                        $groups = Provincial_Slideshow::get_groups(); // Refresh list
                    }
                    break;
            }
        }

        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/slideshow-groups.php';
    }

    /**
     * Add slideshow page
     */
    public function add_slideshow_page() {
        // Check permissions - administrators should always have access
        if (!current_user_can('manage_options') && !current_user_can('manage_provincial_slideshows')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'esp-admin-manager'));
        }

        // Handle form submissions FIRST (before any output)
        if (isset($_POST['action']) && wp_verify_nonce($_POST['_wpnonce'], 'esp_slideshow_action')) {

            if ($_POST['action'] === 'create_tables' && current_user_can('manage_options')) {
                // Handle manual table creation
                $result = Provincial_Slideshow::create_tables();
                $table_status = Provincial_Slideshow::tables_exist();

                if ($table_status['both']) {
                    wp_redirect(add_query_arg(array(
                        'page' => 'provincial-admin-slideshows-add',
                        'message' => 'tables_created'
                    ), admin_url('admin.php')));
                    exit;
                } else {
                    wp_redirect(add_query_arg(array(
                        'page' => 'provincial-admin-slideshows-add',
                        'message' => 'tables_failed'
                    ), admin_url('admin.php')));
                    exit;
                }
            }

            if ($_POST['action'] === 'create_group') {
                // Handle slideshow group creation
                $name = sanitize_text_field($_POST['name']);
                $description = sanitize_textarea_field($_POST['description']);
                $tags = sanitize_text_field($_POST['tags']);

                if (!empty($name)) {
                    $result = Provincial_Slideshow::create_group($name, $description, $tags);

                    if (!is_wp_error($result)) {
                        wp_redirect(add_query_arg(array(
                            'page' => 'provincial-admin-slideshows',
                            'message' => 'group_created'
                        ), admin_url('admin.php')));
                        exit;
                    } else {
                        wp_redirect(add_query_arg(array(
                            'page' => 'provincial-admin-slideshows-add',
                            'message' => 'group_failed',
                            'error' => urlencode($result->get_error_message())
                        ), admin_url('admin.php')));
                        exit;
                    }
                } else {
                    wp_redirect(add_query_arg(array(
                        'page' => 'provincial-admin-slideshows-add',
                        'message' => 'name_required'
                    ), admin_url('admin.php')));
                    exit;
                }
            }
        }

        // Handle success/error messages from redirects
        if (isset($_GET['message'])) {
            switch ($_GET['message']) {
                case 'tables_created':
                    add_settings_error('slideshow_messages', 'slideshow_message',
                        __('Slideshow database tables created successfully.', 'esp-admin-manager'), 'updated');
                    break;
                case 'tables_failed':
                    add_settings_error('slideshow_messages', 'slideshow_error',
                        __('Failed to create slideshow database tables.', 'esp-admin-manager'), 'error');
                    break;
                case 'group_created':
                    add_settings_error('slideshow_messages', 'slideshow_message',
                        __('Slideshow group created successfully.', 'esp-admin-manager'), 'updated');
                    break;
                case 'group_failed':
                    $error_msg = isset($_GET['error']) ? urldecode($_GET['error']) : 'Unknown error';
                    add_settings_error('slideshow_messages', 'slideshow_error',
                        __('Error creating slideshow group: ', 'esp-admin-manager') . $error_msg, 'error');
                    break;
                case 'name_required':
                    add_settings_error('slideshow_messages', 'slideshow_error',
                        __('Group name is required.', 'esp-admin-manager'), 'error');
                    break;
            }
        }

        // Check table status (after form processing)
        $table_status = Provincial_Slideshow::tables_exist();
        if (!$table_status['both']) {
            add_settings_error('slideshow_messages', 'slideshow_warning',
                __('Slideshow database tables are missing. Use the button below to create them.', 'esp-admin-manager'), 'notice-warning');
        }

        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/slideshow-add-group.php';
    }

    /**
     * Handle slideshow AJAX requests
     */
    public function handle_slideshow_ajax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'esp_slideshow_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check permissions
        if (!current_user_can('manage_provincial_slideshows') && !current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $action = sanitize_text_field($_POST['slideshow_action']);

        switch ($action) {
            case 'get_group':
                $group_id = intval($_POST['group_id']);
                $group = Provincial_Slideshow::get_group($group_id);
                wp_send_json_success($group);
                break;

            case 'update_group':
                $group_id = intval($_POST['group_id']);
                $name = sanitize_text_field($_POST['name']);
                $description = sanitize_textarea_field($_POST['description']);
                $tags = sanitize_text_field($_POST['tags']);

                $result = Provincial_Slideshow::update_group($group_id, $name, $description, $tags);
                if ($result) {
                    wp_send_json_success('Group updated successfully');
                } else {
                    wp_send_json_error('Failed to update group');
                }
                break;

            case 'get_slides':
                $group_id = intval($_POST['group_id']);
                $slides = Provincial_Slideshow::get_slides($group_id);
                wp_send_json_success($slides);
                break;

            case 'add_slide':
                $group_id = intval($_POST['group_id']);
                $title = sanitize_text_field($_POST['title']);
                $description = sanitize_textarea_field($_POST['description']);
                $image_url = esc_url_raw($_POST['image_url']);
                $link_url = esc_url_raw($_POST['link_url']);
                $order = intval($_POST['order']);

                $result = Provincial_Slideshow::add_slide($group_id, $title, $description, $image_url, $link_url, $order);
                if (!is_wp_error($result)) {
                    wp_send_json_success('Slide added successfully');
                } else {
                    wp_send_json_error($result->get_error_message());
                }
                break;

            case 'delete_slide':
                $slide_id = intval($_POST['slide_id']);
                $result = Provincial_Slideshow::delete_slide($slide_id);
                if ($result) {
                    wp_send_json_success('Slide deleted successfully');
                } else {
                    wp_send_json_error('Failed to delete slide');
                }
                break;

            case 'toggle_slide_status':
                $slide_id = intval($_POST['slide_id']);
                $result = Provincial_Slideshow::toggle_slide_status($slide_id);
                if ($result) {
                    wp_send_json_success('Slide status updated');
                } else {
                    wp_send_json_error('Failed to update slide status');
                }
                break;

            case 'reorder_slides':
                $slide_orders = $_POST['slide_orders'];
                if (is_array($slide_orders)) {
                    Provincial_Slideshow::reorder_slides($slide_orders);
                    wp_send_json_success('Slides reordered successfully');
                } else {
                    wp_send_json_error('Invalid slide order data');
                }
                break;

            default:
                wp_send_json_error('Invalid action');
        }

        wp_die();
    }

    /**
     * Maps page
     */
    public function maps_page() {
        // Handle JSON file upload
        if (isset($_POST['upload_json']) && wp_verify_nonce($_POST['provincial_nonce'], 'provincial_maps_nonce')) {
            $this->handle_map_json_upload();
        }

        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/maps.php';
    }

    /**
     * Handle map JSON file upload
     */
    private function handle_map_json_upload() {
        if (!isset($_FILES['map_json_file']) || $_FILES['map_json_file']['error'] !== UPLOAD_ERR_OK) {
            add_settings_error('esp_messages', 'esp_message', __('Please select a valid JSON file.', 'esp-admin-manager'), 'error');
            return;
        }

        $file = $_FILES['map_json_file'];
        $map_title = sanitize_text_field($_POST['map_title']);
        $map_description = sanitize_textarea_field($_POST['map_description']);

        // Validate file type
        $file_info = pathinfo($file['name']);
        if (strtolower($file_info['extension']) !== 'json') {
            add_settings_error('esp_messages', 'esp_message', __('Only JSON files are allowed.', 'esp-admin-manager'), 'error');
            return;
        }

        // Create upload directory
        $upload_dir = wp_upload_dir();
        $maps_dir = $upload_dir['basedir'] . '/provincial-maps/';
        if (!file_exists($maps_dir)) {
            wp_mkdir_p($maps_dir);
        }

        // Generate unique filename
        $filename = time() . '_' . sanitize_file_name($file['name']);
        $file_path = $maps_dir . $filename;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            // Create map post
            $map_id = wp_insert_post(array(
                'post_title' => $map_title,
                'post_content' => $map_description,
                'post_status' => 'publish',
                'post_type' => 'esp_map'
            ));

            if ($map_id) {
                update_post_meta($map_id, '_esp_map_json_file', $filename);
                update_post_meta($map_id, '_esp_map_json_path', $file_path);
                add_settings_error('esp_messages', 'esp_message', __('Map uploaded successfully.', 'esp-admin-manager'), 'updated');
            }
        } else {
            add_settings_error('esp_messages', 'esp_message', __('Failed to upload file.', 'esp-admin-manager'), 'error');
        }
    }

}
