<?php
/**
 * Provincial Administration Post Types Class
 * 
 * Handles registration of custom post types for the Provincial Administration Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Post_Types {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'register_post_types'), 0);
        add_action('admin_init', array($this, 'maybe_flush_rewrite_rules'));
        add_action('admin_menu', array($this, 'hide_post_types_from_district_users'), 999);

        // Also register immediately if we're past init
        if (did_action('init')) {
            $this->register_post_types();
        }
    }
    
    /**
     * Register all custom post types
     */
    public function register_post_types() {
        // Debug: Log that this function is being called
        error_log('ESP: Registering post types...');

        $this->register_governor_post_type();
        $this->register_mp_post_type();
        $this->register_district_post_type();
        $this->register_contact_post_type();
        $this->register_event_post_type();
        $this->register_news_post_type();
        $this->register_map_post_type();

        // Debug: Check if post types were registered
        $registered_types = get_post_types();
        if (in_array('esp_mp', $registered_types)) {
            error_log('ESP: esp_mp post type successfully registered');
        } else {
            error_log('ESP: esp_mp post type FAILED to register');
        }

        if (in_array('esp_contact', $registered_types)) {
            error_log('ESP: esp_contact post type successfully registered');
        } else {
            error_log('ESP: esp_contact post type FAILED to register');
        }
    }
    
    /**
     * Register Governor post type
     */
    private function register_governor_post_type() {
        register_post_type('esp_governor', array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'labels'             => array(
                'name'               => 'Governors',
                'singular_name'      => 'Governor',
                'add_new_item'       => 'Add New Governor',
                'edit_item'          => 'Edit Governor',
                'all_items'          => 'All Governors',
            ),
        ));
    }
    
    /**
     * Register MP post type
     */
    private function register_mp_post_type() {
        $args = array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'labels'             => array(
                'name'               => 'MPs',
                'singular_name'      => 'MP',
                'add_new_item'       => 'Add New MP',
                'edit_item'          => 'Edit MP',
                'all_items'          => 'All MPs',
            ),
        );

        $result = register_post_type('esp_mp', $args);

        // Debug: Check if registration was successful
        if (is_wp_error($result)) {
            error_log('ESP: esp_mp registration failed: ' . $result->get_error_message());
        } else {
            error_log('ESP: esp_mp registration successful');
        }
    }
    
    /**
     * Register District post type
     */
    private function register_district_post_type() {
        register_post_type('esp_district', array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'labels'             => array(
                'name'               => 'Districts',
                'singular_name'      => 'District',
                'add_new_item'       => 'Add New District',
                'edit_item'          => 'Edit District',
                'all_items'          => 'All Districts',
            ),
        ));
    }

    /**
     * Register Contact post type
     */
    private function register_contact_post_type() {
        register_post_type('esp_contact', array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'labels'             => array(
                'name'               => 'Contacts',
                'singular_name'      => 'Contact',
                'add_new_item'       => 'Add New Contact',
                'edit_item'          => 'Edit Contact',
                'all_items'          => 'All Contacts',
                'view_item'          => 'View Contact',
                'search_items'       => 'Search Contacts',
                'not_found'          => 'No contacts found',
                'not_found_in_trash' => 'No contacts found in trash',
            ),
            'menu_icon'          => 'dashicons-phone',
            'has_archive'        => false,
            'rewrite'            => array('slug' => 'contacts'),
        ));
    }
    
    /**
     * Register Event post type
     */
    private function register_event_post_type() {
        register_post_type('esp_event', array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'edit_posts',
                'read_post'          => 'read',
                'delete_post'        => 'delete_posts',
                'edit_posts'         => 'edit_posts',
                'edit_others_posts'  => 'edit_others_posts',
                'publish_posts'      => 'publish_posts',
                'read_private_posts' => 'read_private_posts',
            ),
            'labels'             => array(
                'name'               => 'Events',
                'singular_name'      => 'Event',
                'add_new_item'       => 'Add New Event',
                'edit_item'          => 'Edit Event',
                'all_items'          => 'All Events',
            ),
        ));
    }
    
    /**
     * Register News post type
     */
    private function register_news_post_type() {
        register_post_type('esp_news', array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
            'capability_type'    => 'post',
            'capabilities'       => array(
                'edit_post'          => 'edit_posts',
                'read_post'          => 'read',
                'delete_post'        => 'delete_posts',
                'edit_posts'         => 'edit_posts',
                'edit_others_posts'  => 'edit_others_posts',
                'publish_posts'      => 'publish_posts',
                'read_private_posts' => 'read_private_posts',
            ),
            'labels'             => array(
                'name'               => 'News',
                'singular_name'      => 'News',
                'add_new_item'       => 'Add New News',
                'edit_item'          => 'Edit News',
                'all_items'          => 'All News',
            ),
        ));
    }

    /**
     * Register Map post type
     */
    private function register_map_post_type() {
        register_post_type('esp_map', array(
            'public'             => true,
            'show_ui'            => true,
            'show_in_menu'       => false, // Managed through custom admin interface
            'supports'           => array('title', 'editor', 'thumbnail'),
            'labels'             => array(
                'name'               => __('Maps', 'esp-admin-manager'),
                'singular_name'      => __('Map', 'esp-admin-manager'),
                'add_new_item'       => __('Add New Map', 'esp-admin-manager'),
                'edit_item'          => __('Edit Map', 'esp-admin-manager'),
                'all_items'          => __('All Maps', 'esp-admin-manager'),
                'view_item'          => __('View Map', 'esp-admin-manager'),
                'search_items'       => __('Search Maps', 'esp-admin-manager'),
                'not_found'          => __('No maps found', 'esp-admin-manager'),
                'not_found_in_trash' => __('No maps found in trash', 'esp-admin-manager'),
            ),
            'menu_icon'          => 'dashicons-location-alt',
            'has_archive'        => true,
            'rewrite'            => array('slug' => 'maps'),
        ));
    }

    /**
     * Maybe flush rewrite rules if needed
     */
    public function maybe_flush_rewrite_rules() {
        if (get_option('esp_flush_rewrite_rules', false)) {
            flush_rewrite_rules();
            delete_option('esp_flush_rewrite_rules');
        }
    }

    /**
     * Force flush rewrite rules
     */
    public static function flush_rewrite_rules() {
        update_option('esp_flush_rewrite_rules', true);
    }

    /**
     * Force register post types (for testing)
     */
    public function force_register_post_types() {
        $this->register_post_types();
        wp_die('Post types registered. <a href="' . admin_url() . '">Go back</a>');
    }

    /**
     * Hide custom post types from district users
     */
    public function hide_post_types_from_district_users() {
        // Get current user information
        $current_user_id = get_current_user_id();
        $user_type = get_user_meta($current_user_id, 'provincial_user_type', true);

        // Only hide for district users (not for admins or provincial users)
        if ($user_type !== 'district' || current_user_can('manage_options')) {
            return;
        }

        // List of post types to hide from district users
        $post_types_to_hide = array(
            'esp_governor',
            'esp_mp',
            'esp_district',  // Hide default districts menu - they use custom interface
            'esp_contact',
            'esp_event',
            'esp_news'
        );

        // Remove menu pages for these post types
        foreach ($post_types_to_hide as $post_type) {
            remove_menu_page('edit.php?post_type=' . $post_type);
        }

        // Also hide some default WordPress menus that district users don't need
        remove_menu_page('edit.php'); // Regular posts
        remove_menu_page('edit.php?post_type=page'); // Pages
        remove_menu_page('edit-comments.php'); // Comments
        remove_menu_page('themes.php'); // Appearance
        remove_menu_page('plugins.php'); // Plugins
        remove_menu_page('tools.php'); // Tools
        remove_menu_page('options-general.php'); // Settings

        // Keep Media library available - they might need it for district photos
        // Keep Users menu available - they might need to see their profile
    }
}
